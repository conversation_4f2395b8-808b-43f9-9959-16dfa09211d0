from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from products_service.routes import router as products_router
from products_service.auth_middleware import cleanup_auth_service
from shared.config import settings
from shared.database import engine, Base
from datetime import datetime
from sqlalchemy import text

app = FastAPI(title="Products Microservice")

# CORS setup (adjust origins as needed)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(products_router, prefix="/products", tags=["products"])


# Create database tables
@app.on_event("startup")
async def startup():
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


@app.on_event("shutdown")
async def shutdown():
    """Cleanup resources on shutdown."""
    await cleanup_auth_service()


# Health check endpoint
@app.get("/")
async def health_check():
    """Health check endpoint that verifies service and database connectivity."""
    timestamp = datetime.utcnow().isoformat() + "Z"

    try:
        # Test database connection
        async with engine.begin() as conn:
            await conn.execute(text("SELECT 1"))

        return {
            "status": "healthy",
            "service": "products-microservice",
            "version": "1.0.0",
            "database": "connected",
            "timestamp": timestamp,
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "products-microservice",
            "version": "1.0.0",
            "database": "disconnected",
            "error": str(e),
            "timestamp": timestamp,
        }


# Future products routes can be added here
# app.include_router(products_router, prefix="/products", tags=["products"])
