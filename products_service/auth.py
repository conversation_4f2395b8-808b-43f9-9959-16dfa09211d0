"""
Simple JWT authentication for products service.
Verifies tokens using external auth service and JWKS.
"""

import httpx
import time
from typing import Optional, Dict, Any
from fastapi import HTTPException, status, Depends, Header
from pydantic import BaseModel


class AuthUser(BaseModel):
    """Simple authenticated user model."""
    email: str
    roles: list = []
    scope: list = []


# Cache for JWKS and token verification
_cache: Dict[str, Any] = {}

# External auth service URLs
AUTH_TOKEN_VERIFY_URL = "https://fehdan-auth-service.onrender.com/api/v1/auth/verify-token"
AUTH_JWKS_URL = "https://fehdan-auth-service.onrender.com/api/v1/auth/.well-known/jwks.json"


async def verify_token(token: str) -> AuthUser:
    """
    Verify JWT token with external auth service.
    
    Args:
        token: JWT token to verify
        
    Returns:
        AuthUser: Authenticated user information
        
    Raises:
        HTTPException: If token verification fails
    """
    # Check cache first
    cache_key = f"token_{hash(token)}"
    current_time = time.time()
    
    if cache_key in _cache:
        cached_data = _cache[cache_key]
        if current_time - cached_data["timestamp"] < 60:  # 1 minute cache
            return cached_data["user"]
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post(
                AUTH_TOKEN_VERIFY_URL,
                json={"token": token},
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 401:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid or expired token",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            response.raise_for_status()
            data = response.json()
            
            if not data.get("active", False):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token is not active",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            user = AuthUser(
                email=data.get("sub", ""),
                roles=data.get("roles", []),
                scope=data.get("scope", [])
            )
            
            # Cache the result
            _cache[cache_key] = {
                "user": user,
                "timestamp": current_time
            }
            
            return user
            
    except httpx.HTTPError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token verification failed",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication service error",
        )


async def get_current_user(
    authorization: Optional[str] = Header(None, description="Bearer token")
) -> AuthUser:
    """
    FastAPI dependency to get current authenticated user.
    
    Args:
        authorization: Authorization header containing Bearer token
        
    Returns:
        AuthUser: Authenticated user information
        
    Raises:
        HTTPException: If authentication fails
    """
    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authorization header is required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Extract token from Authorization header
    try:
        scheme, token = authorization.split(" ", 1)
        if scheme.lower() != "bearer":
            raise ValueError("Invalid scheme")
        token = token.strip()
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authorization header format. Expected 'Bearer <token>'",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return await verify_token(token)


async def require_write_access(user: AuthUser = Depends(get_current_user)) -> AuthUser:
    """
    Dependency that requires write access (write scope or admin role).
    
    Args:
        user: Current authenticated user
        
    Returns:
        AuthUser: Authenticated user with write access
        
    Raises:
        HTTPException: If user doesn't have write access
    """
    if "write" not in user.scope and "admin" not in user.roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Write access required. Need 'write' scope or 'admin' role.",
        )
    return user
