"""
Logging utilities for the products service.

This module provides logging functionality independent from shared logging components.
"""

import logging
import sys
from typing import Optional, Any
from products_service.config import settings


class LoggingService:
    """Simple logging service for the products service."""
    
    def __init__(self):
        self.logger = logging.getLogger("products_service")
        if not self.logger.handlers:
            self._setup_logger()
    
    def _setup_logger(self):
        """Set up the logger with appropriate handlers and formatters."""
        # Set log level based on debug setting
        level = logging.DEBUG if settings.DEBUG else logging.INFO
        self.logger.setLevel(level)
        
        # Create console handler
        handler = logging.StreamHandler(sys.stdout)
        handler.setLevel(level)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        
        # Add handler to logger
        self.logger.addHandler(handler)
    
    def log(
        self, 
        message: str, 
        level: str = "info", 
        exception: Optional[Exception] = None,
        app_name: Optional[str] = None,
        **kwargs
    ):
        """
        Log a message with the specified level.
        
        Args:
            message: The message to log
            level: Log level (debug, info, warning, error, critical)
            exception: Optional exception to log
            app_name: Optional app name (for compatibility)
            **kwargs: Additional keyword arguments (ignored for compatibility)
        """
        log_method = getattr(self.logger, level.lower(), self.logger.info)
        
        if exception:
            log_method(f"{message}: {str(exception)}", exc_info=True)
        else:
            log_method(message)
