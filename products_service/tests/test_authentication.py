"""
Test suite for JWT authentication middleware and dependencies.
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi import HTTPException
from products_service.auth_middleware import AuthenticationService
from products_service.auth_dependencies import (
    get_current_user,
    require_roles,
    require_scopes,
    require_write_access,
    require_admin_access,
)
from products_service.auth_models import (
    TokenVerificationResponse,
    JWKSResponse,
    AuthenticatedUser,
    AuthenticationError,
)


class TestAuthenticationService:
    """Test cases for AuthenticationService."""
    
    @pytest.fixture
    def auth_service(self):
        """Create AuthenticationService instance for testing."""
        return AuthenticationService()
    
    def test_extract_token_from_header_valid(self, auth_service):
        """Test extracting valid token from Authorization header."""
        header = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
        token = auth_service.extract_token_from_header(header)
        assert token == "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
    
    def test_extract_token_from_header_invalid_format(self, auth_service):
        """Test extracting token from invalid Authorization header format."""
        # Missing Bearer prefix
        assert auth_service.extract_token_from_header("token123") is None
        
        # Wrong scheme
        assert auth_service.extract_token_from_header("Basic token123") is None
        
        # Empty header
        assert auth_service.extract_token_from_header("") is None
        assert auth_service.extract_token_from_header(None) is None
    
    @pytest.mark.asyncio
    async def test_get_jwks_public_key_success(self, auth_service):
        """Test successful JWKS public key retrieval."""
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "public_key": "-----BEGIN PUBLIC KEY-----\ntest_key\n-----END PUBLIC KEY-----"
        }
        mock_response.raise_for_status.return_value = None
        
        with patch.object(auth_service, '_get_http_client') as mock_client:
            mock_client.return_value.get = AsyncMock(return_value=mock_response)
            
            public_key = await auth_service.get_jwks_public_key()
            assert "-----BEGIN PUBLIC KEY-----" in public_key
            assert "test_key" in public_key
    
    @pytest.mark.asyncio
    async def test_get_jwks_public_key_http_error(self, auth_service):
        """Test JWKS public key retrieval with HTTP error."""
        import httpx
        with patch.object(auth_service, '_get_http_client') as mock_client:
            mock_client.return_value.get = AsyncMock(side_effect=httpx.HTTPError("HTTP Error"))

            with pytest.raises(AuthenticationError, match="Unable to retrieve authentication keys"):
                await auth_service.get_jwks_public_key()
    
    @pytest.mark.asyncio
    async def test_verify_token_with_service_success(self, auth_service):
        """Test successful token verification."""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "active": True,
            "sub": "<EMAIL>",
            "roles": ["customer"],
            "scope": ["read", "write"]
        }
        mock_response.raise_for_status.return_value = None
        
        with patch.object(auth_service, '_get_http_client') as mock_client:
            mock_client.return_value.post = AsyncMock(return_value=mock_response)
            
            result = await auth_service.verify_token_with_service("test_token")
            assert result.active is True
            assert result.sub == "<EMAIL>"
            assert "customer" in result.roles
            assert "read" in result.scope
    
    @pytest.mark.asyncio
    async def test_verify_token_with_service_invalid_token(self, auth_service):
        """Test token verification with invalid token."""
        mock_response = MagicMock()
        mock_response.status_code = 401
        
        with patch.object(auth_service, '_get_http_client') as mock_client:
            mock_client.return_value.post = AsyncMock(return_value=mock_response)
            
            with pytest.raises(AuthenticationError, match="Invalid or expired token"):
                await auth_service.verify_token_with_service("invalid_token")
    
    @pytest.mark.asyncio
    async def test_verify_token_with_service_inactive_token(self, auth_service):
        """Test token verification with inactive token."""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "active": False,
            "sub": "<EMAIL>",
            "roles": [],
            "scope": []
        }
        mock_response.raise_for_status.return_value = None
        
        with patch.object(auth_service, '_get_http_client') as mock_client:
            mock_client.return_value.post = AsyncMock(return_value=mock_response)
            
            with pytest.raises(AuthenticationError, match="Token is not active"):
                await auth_service.verify_token_with_service("inactive_token")
    
    @pytest.mark.asyncio
    async def test_authenticate_user_success(self, auth_service):
        """Test successful user authentication."""
        with patch.object(auth_service, 'verify_token_with_service') as mock_verify:
            mock_verify.return_value = TokenVerificationResponse(
                active=True,
                sub="<EMAIL>",
                roles=["customer"],
                scope=["read", "write"]
            )
            
            user = await auth_service.authenticate_user("test_token")
            assert isinstance(user, AuthenticatedUser)
            assert user.email == "<EMAIL>"
            assert user.has_role("customer")
            assert user.has_scope("read")
            assert user.is_authenticated is True


class TestAuthenticatedUser:
    """Test cases for AuthenticatedUser model."""
    
    def test_has_role(self):
        """Test role checking functionality."""
        user = AuthenticatedUser(
            email="<EMAIL>",
            roles=["customer", "premium"],
            scope=["read"]
        )
        
        assert user.has_role("customer") is True
        assert user.has_role("admin") is False
    
    def test_has_scope(self):
        """Test scope checking functionality."""
        user = AuthenticatedUser(
            email="<EMAIL>",
            roles=["customer"],
            scope=["read", "write"]
        )
        
        assert user.has_scope("read") is True
        assert user.has_scope("admin") is False
    
    def test_has_any_role(self):
        """Test multiple role checking functionality."""
        user = AuthenticatedUser(
            email="<EMAIL>",
            roles=["customer"],
            scope=["read"]
        )
        
        assert user.has_any_role(["customer", "admin"]) is True
        assert user.has_any_role(["admin", "super_admin"]) is False
    
    def test_has_any_scope(self):
        """Test multiple scope checking functionality."""
        user = AuthenticatedUser(
            email="<EMAIL>",
            roles=["customer"],
            scope=["read"]
        )
        
        assert user.has_any_scope(["read", "write"]) is True
        assert user.has_any_scope(["write", "admin"]) is False


class TestAuthenticationDependencies:
    """Test cases for authentication dependencies."""
    
    @pytest.mark.asyncio
    async def test_get_current_user_missing_header(self):
        """Test get_current_user with missing Authorization header."""
        with pytest.raises(HTTPException) as exc_info:
            await get_current_user(authorization=None)
        
        assert exc_info.value.status_code == 401
        assert "Authorization header is required" in exc_info.value.detail
    
    @pytest.mark.asyncio
    async def test_get_current_user_invalid_header_format(self):
        """Test get_current_user with invalid Authorization header format."""
        with pytest.raises(HTTPException) as exc_info:
            await get_current_user(authorization="InvalidFormat")
        
        assert exc_info.value.status_code == 401
        assert "Invalid authorization header format" in exc_info.value.detail
    
    @pytest.mark.asyncio
    async def test_require_roles_success(self):
        """Test require_roles dependency with valid roles."""
        user = AuthenticatedUser(
            email="<EMAIL>",
            roles=["admin"],
            scope=["read", "write"]
        )
        
        # Mock the get_current_user dependency
        with patch('products_service.auth_dependencies.get_current_user', return_value=user):
            dependency = require_roles(["admin", "super_admin"])
            result = await dependency()
            assert result == user
    
    @pytest.mark.asyncio
    async def test_require_roles_insufficient_permissions(self):
        """Test require_roles dependency with insufficient roles."""
        user = AuthenticatedUser(
            email="<EMAIL>",
            roles=["customer"],
            scope=["read"]
        )
        
        # Mock the get_current_user dependency
        with patch('products_service.auth_dependencies.get_current_user', return_value=user):
            dependency = require_roles(["admin"])
            
            with pytest.raises(HTTPException) as exc_info:
                await dependency()
            
            assert exc_info.value.status_code == 403
            assert "Access denied" in exc_info.value.detail
    
    @pytest.mark.asyncio
    async def test_require_scopes_success(self):
        """Test require_scopes dependency with valid scopes."""
        user = AuthenticatedUser(
            email="<EMAIL>",
            roles=["customer"],
            scope=["read", "write"]
        )
        
        # Mock the get_current_user dependency
        with patch('products_service.auth_dependencies.get_current_user', return_value=user):
            dependency = require_scopes(["write"])
            result = await dependency()
            assert result == user
    
    @pytest.mark.asyncio
    async def test_require_write_access_with_write_scope(self):
        """Test require_write_access with write scope."""
        user = AuthenticatedUser(
            email="<EMAIL>",
            roles=["customer"],
            scope=["read", "write"]
        )
        
        # Mock the get_current_user dependency
        with patch('products_service.auth_dependencies.get_current_user', return_value=user):
            dependency = require_write_access()
            result = await dependency()
            assert result == user
    
    @pytest.mark.asyncio
    async def test_require_write_access_with_admin_role(self):
        """Test require_write_access with admin role."""
        user = AuthenticatedUser(
            email="<EMAIL>",
            roles=["admin"],
            scope=["read"]
        )
        
        # Mock the get_current_user dependency
        with patch('products_service.auth_dependencies.get_current_user', return_value=user):
            dependency = require_write_access()
            result = await dependency()
            assert result == user
    
    @pytest.mark.asyncio
    async def test_require_admin_access_success(self):
        """Test require_admin_access with admin role."""
        user = AuthenticatedUser(
            email="<EMAIL>",
            roles=["admin"],
            scope=["read", "write"]
        )

        # Mock the get_current_user dependency
        with patch('products_service.auth_dependencies.get_current_user', return_value=user):
            dependency = require_admin_access()
            result = await dependency()
            assert result == user


class TestProtectedRoutes:
    """Integration tests for protected product routes."""

    @pytest.fixture
    def mock_auth_service(self):
        """Mock authentication service for testing."""
        with patch('products_service.auth_middleware.auth_service') as mock:
            yield mock

    def test_create_product_without_auth_fails(self, client):
        """Test that creating a product without authentication fails."""
        product_data = {
            "name": "Test Product",
            "price": 10.99,
            "description": "Test description"
        }

        response = client.post("/products/", json=product_data)
        assert response.status_code == 401
        assert "Authorization header is required" in response.json()["detail"]

    def test_update_product_without_auth_fails(self, client):
        """Test that updating a product without authentication fails."""
        update_data = {
            "name": "Updated Product",
            "price": 15.99
        }

        response = client.put("/products/1", json=update_data)
        assert response.status_code == 401
        assert "Authorization header is required" in response.json()["detail"]

    def test_delete_product_without_auth_fails(self, client):
        """Test that deleting a product without authentication fails."""
        response = client.delete("/products/1")
        assert response.status_code == 401
        assert "Authorization header is required" in response.json()["detail"]

    def test_list_products_without_auth_succeeds(self, client):
        """Test that listing products works without authentication."""
        response = client.get("/products/")
        # Should not fail with authentication error
        assert response.status_code != 401

    def test_get_product_without_auth_succeeds(self, client):
        """Test that getting a single product works without authentication."""
        response = client.get("/products/1")
        # Should not fail with authentication error (might fail with 404 if product doesn't exist)
        assert response.status_code != 401
