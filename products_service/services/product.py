from typing import Optional, List
import time

from sqlalchemy import String, and_, or_, select
from sqlalchemy.orm import selectinload
from fastapi import HTTPException
from shared.core.utils.logging import LoggingService
from shared.events import Event, PRODUCT_CREATED, PRODUCT_UPDATED, PRODUCT_DELETED
from shared.redis_client import publish_event
from products_service.model import Product, Category
from products_service.schemas import ProductUpdate, ProductCreate, ProductResponse
from products_service.repositories.product import ProductRepository
from products_service.repositories.category import CategoryRepository
from sqlalchemy.ext.asyncio import AsyncSession


class ProductService:
    """
    Service layer for product management operations.

    This service handles business logic for product operations including:
    - Product creation, update, deletion
    - Category management integration
    - Event publishing for other services
    - Product search and filtering

    The service follows the Single Responsibility Principle by focusing on
    business logic while delegating data access to repository layers.
    """

    def __init__(self, db: AsyncSession):
        """
        Initialize ProductService with required dependencies.

        Args:
            db (AsyncSession): Database session for repository operations
        """
        self.product_repo = ProductRepository(db)
        self.category_repo = CategoryRepository(db)
        self.logger = LoggingService()
        self.db = db

    async def _validate_and_process_categories(self, category_names: Optional[List[str]]) -> List[str]:
        """
        Validate and process category names.

        Args:
            category_names: List of category names to validate

        Returns:
            List of validated category names

        Raises:
            HTTPException: If category validation fails
        """
        if not category_names:
            return []

        validated_categories = []
        for name in category_names:
            if not name or not isinstance(name, str):
                continue

            name = name.strip()
            if not name:
                continue

            if len(name) > 100:
                raise HTTPException(
                    status_code=400,
                    detail=f"Category name '{name}' is too long (max 100 characters)"
                )

            validated_categories.append(name)

        return validated_categories

    async def _ensure_categories_exist(self, category_names: List[str]) -> List[Category]:
        """
        Ensure all categories exist in the database, creating them if necessary.

        Args:
            category_names: List of category names to ensure exist

        Returns:
            List of Category objects

        Raises:
            HTTPException: If category creation fails
        """
        try:
            return await self.category_repo.get_or_create_categories(category_names)
        except Exception as e:
            self.logger.log(
                message=f"Error ensuring categories exist: {category_names}",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(
                status_code=500,
                detail="Failed to process product categories"
            )

    async def _assign_categories_to_product(self, product_id: int, category_names: List[str]) -> None:
        """
        Assign categories to a product by managing the association table directly.

        Args:
            product_id: ID of the product to assign categories to
            category_names: List of category names to assign

        Raises:
            HTTPException: If category assignment fails
        """
        try:
            from sqlalchemy import insert
            from products_service.model import product_categories

            categories = await self._ensure_categories_exist(category_names)

            for category in categories:
                await self.db.execute(
                    insert(product_categories).values(
                        product_id=product_id,
                        category_id=category.id
                    )
                )
            await self.db.commit()

        except Exception as e:
            await self.db.rollback()
            self.logger.log(
                message=f"Error assigning categories to product {product_id}: {category_names}",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(
                status_code=500,
                detail="Failed to assign categories to product"
            )

    async def _update_product_categories(self, product_id: int, category_names: List[str]) -> None:
        """
        Update categories for a product by clearing existing and adding new ones.

        Args:
            product_id: ID of the product to update categories for
            category_names: List of new category names

        Raises:
            HTTPException: If category update fails
        """
        try:
            from sqlalchemy import delete, insert
            from products_service.model import product_categories

            await self.db.execute(
                delete(product_categories).where(product_categories.c.product_id == product_id)
            )

            if category_names:
                categories = await self._ensure_categories_exist(category_names)
                for category in categories:
                    await self.db.execute(
                        insert(product_categories).values(
                            product_id=product_id,
                            category_id=category.id
                        )
                    )

            await self.db.commit()

        except Exception as e:
            await self.db.rollback()
            self.logger.log(
                message=f"Error updating categories for product {product_id}: {category_names}",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(
                status_code=500,
                detail="Failed to update product categories"
            )

    async def create_product(
        self, current_user, product_data: ProductCreate
    ) -> ProductResponse:
        """
        Create a new product and publish event for inventory service.

        This method handles the complete product creation workflow including:
        - Category validation and creation
        - Product data validation
        - Database transaction management
        - Event publishing for other services

        Args:
            current_user: The user creating the product
            product_data (ProductCreate): Data for the new product

        Returns:
            ProductResponse: The created product with all relationships

        Raises:
            HTTPException: If validation fails or creation errors occur
        """
        try:
            await self.validate_product_data(product_data)

            validated_categories = await self._validate_and_process_categories(
                product_data.categories
            )

            if validated_categories:
                await self._ensure_categories_exist(validated_categories)

            self.logger.log(
                message=f"Creating product '{product_data.name}' with categories: {validated_categories}",
                level="info",
                app_name="products",
            )

            new_product = await self.product_repo.create_product(product_data)

            if new_product:
                if validated_categories:
                    await self._assign_categories_to_product(new_product.id, validated_categories)
                    new_product = await self.product_repo.get_product_by_id(new_product.id)
                event_data = {
                    "product_id": new_product.id,
                    "product_name": new_product.name,
                    "stock_quantity": new_product.stock_quantity,
                    "price": float(new_product.price),
                    "currency": new_product.currency,
                    "user_id": getattr(
                        current_user, "id", 1
                    ),  # Default to 1 if no user ID 
                    "location_id": 1,  # TODO: get from user's location
                    "reorder_point": 10,  # Default reorder point
                    "created_by": getattr(current_user, "email", "system"),
                }

                event = Event(
                    type=PRODUCT_CREATED, data=event_data, timestamp=time.time()
                )

                await publish_event(event, channel="product_events")

                self.logger.log(
                    f"Product created successfully with ID: {new_product.id} and event published",
                    level="info",
                    app_name="products",
                )
                return ProductResponse.model_validate(new_product)
            else:
                self.logger.log(
                    "Failed to create product",
                    level="error",
                    app_name="products",
                )
                raise HTTPException(
                    status_code=500, detail="Failed to create product in database"
                )
        except HTTPException:
            raise
        except Exception as e:
            self.logger.log(
                f"Unexpected error creating product: {str(e)}",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(
                status_code=500, detail=f"Unexpected error creating product: {str(e)}"
            )

    async def get_product(self, product_id: int) -> ProductResponse:
        """Retrieve product details by ID."""
        try:
            product = await self.product_repo.get_product_by_id(product_id)
            return product
        except HTTPException:
            raise
        except Exception as e:
            self.logger.log(
                f"Error retrieving product {product_id}: {str(e)}",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(
                status_code=500,
                detail=f"Failed to retrieve product {product_id}"
            )

    async def update_product(
        self, product_id: int, product_data: dict, current_user: Optional[dict] = None
    ) -> ProductResponse:
        """
        Update product details and publish event.

        This method handles the complete product update workflow including:
        - Category validation and creation for new categories
        - Product data validation
        - Database transaction management
        - Event publishing for other services

        Args:
            product_id (int): ID of the product to update
            product_data (dict): Updated product data
            current_user: The user updating the product (optional)

        Returns:
            ProductResponse: The updated product with all relationships

        Raises:
            HTTPException: If validation fails or update errors occur
        """
        try:
            product_update_data = ProductUpdate.model_validate(product_data)

            categories_to_update = None
            if hasattr(product_update_data, 'categories') and product_update_data.categories is not None:
                validated_categories = await self._validate_and_process_categories(
                    product_update_data.categories
                )
                categories_to_update = validated_categories

                self.logger.log(
                    message=f"Updating product {product_id} with categories: {validated_categories}",
                    level="info",
                    app_name="products",
                )

            updated_product = await self.product_repo.update_product(
                product_id, product_update_data
            )

            if categories_to_update is not None:
                await self._update_product_categories(product_id, categories_to_update)
                updated_product = await self.product_repo.get_product_by_id(product_id)

            if updated_product:
                event_data = {
                    "product_id": updated_product.id,
                    "product_name": updated_product.name,
                    "stock_quantity": updated_product.stock_quantity,
                    "price": float(updated_product.price),
                    "currency": updated_product.currency,
                    "updated_fields": product_data,
                }

                event = Event(
                    type=PRODUCT_UPDATED, data=event_data, timestamp=time.time()
                )

                await publish_event(event, channel="product_events")

                self.logger.log(
                    f"Product {product_id} updated successfully and event published",
                    level="info",
                    app_name="products",
                )

            return updated_product
        except HTTPException:
            raise
        except Exception as e:
            self.logger.log(
                f"Error updating product {product_id}: {str(e)}",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(
                status_code=500,
                detail=f"Failed to update product {product_id}"
            )

    async def list_products(
        self, skip: int = 0, limit: int = 100, filter: Optional[dict] = None
    ) -> List[ProductResponse]:
        """
        List products with optional filtering and pagination.

        This method provides comprehensive product listing with support for:
        - Category-based filtering
        - Stock status filtering
        - Text search across name and description
        - Pagination support

        Args:
            skip (int): Number of records to skip for pagination
            limit (int): Maximum number of records to return
            filter (Optional[dict]): Filter criteria including category, in_stock, search

        Returns:
            List[ProductResponse]: List of products matching the criteria

        Raises:
            HTTPException: If filtering or database errors occur
        """
        try:
            query = select(Product).options(selectinload(Product.categories))
            conditions = []

            if filter:
                if filter.get("category"):
                    category_name = filter["category"].strip()
                    if category_name:
                        category = await self.category_repo.get_category_by_name(category_name)
                        if not category:
                            self.logger.log(
                                message=f"Filter requested for non-existent category: {category_name}",
                                level="warning",
                                app_name="products",
                            )
                            return []

                        query = query.join(Product.categories)
                        conditions.append(Category.name == category_name)

                if filter.get("in_stock") is not None:
                    conditions.append(Product.in_stock == filter["in_stock"])

                if filter.get("search"):
                    search_term = filter["search"].strip()
                    if search_term:
                        search_pattern = f"%{search_term}%"
                        search_condition = (
                            Product.name.ilike(search_pattern) |
                            Product.description.ilike(search_pattern)
                        )
                        conditions.append(search_condition)

                if conditions:
                    query = query.where(and_(*conditions))

            query = query.offset(skip).limit(limit)

            result = await self.db.execute(query)
            products = [
                ProductResponse.model_validate(prod) for prod in result.scalars().all()
            ]

            self.logger.log(
                message=f"Listed {len(products)} products with filters: {filter}",
                level="info",
                app_name="products",
            )

            return products

        except Exception as e:
            self.logger.log(
                message=f"Error listing products with filters {filter}",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(
                status_code=500,
                detail="Failed to retrieve products"
            )

    async def delete_product(self, product_id: int, current_user=None) -> bool:
        """Delete product and publish event."""
        try:
            product = await self.product_repo.get_product_by_id(product_id)

            await self.product_repo.delete_product(product_id)
            event_data = {
                "product_id": product_id,
                "product_name": product.name,
            }

            event = Event(type=PRODUCT_DELETED, data=event_data, timestamp=time.time())

            await publish_event(event, channel="product_events")

            self.logger.log(
                f"Product {product_id} deleted successfully and event published",
                level="info",
                app_name="products",
            )
            return True
        except Exception as e:
            self.logger.log(
                f"Error deleting product {product_id}: {str(e)}",
                level="error",
                exception=e,
                app_name="products",
            )
            return False

    async def get_product_stock_level(self, product_id: int) -> int:
        """Get current stock level for a product."""
        try:
            stock_level = await self.inventory_service.get_stock_level(product_id)
            return stock_level
        except Exception as e:
            self.logger.log(
                f"Error retrieving stock level for product {product_id}: {str(e)}",
                level="error",
                exception=e,
                app_name="products",
            )
            return 0

    async def search_products(self, query: str, limit: int = 50) -> List[ProductResponse]:
        """
        Search products by name, description, tags, or categories.

        This method provides comprehensive product search across multiple fields:
        - Product name (case-insensitive)
        - Product description (case-insensitive)
        - Product tags (JSON array search)
        - Category names (case-insensitive)

        Args:
            query (str): Search term to look for
            limit (int): Maximum number of results to return

        Returns:
            List[ProductResponse]: List of products matching the search criteria

        Raises:
            HTTPException: If search fails due to database errors
        """
        try:
            if not query or not query.strip():
                return []

            search_term = query.strip()
            search_pattern = f"%{search_term}%"

            base_conditions = [
                Product.name.ilike(search_pattern),
                Product.description.ilike(search_pattern)
            ]

            tag_condition = and_(
                Product.tags.is_not(None),
                Product.tags.cast(String) != '[]',
                Product.tags.cast(String).ilike(f'%"{search_term}"%')
            )

            category_condition = and_(
                Product.categories.any(Category.name.ilike(search_pattern))
            )

            search_query = select(Product).options(selectinload(Product.categories)).where(
                or_(*base_conditions, tag_condition, category_condition)
            ).limit(limit)

            result = await self.db.execute(search_query)
            products = [
                ProductResponse.model_validate(prod) for prod in result.scalars().all()
            ]

            self.logger.log(
                message=f"Search for '{search_term}' returned {len(products)} products",
                level="info",
                app_name="products",
            )

            return products

        except Exception as e:
            self.logger.log(
                message=f"Error searching products with query '{query}'",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(
                status_code=500,
                detail="Failed to search products"
            )

    async def get_categories(self, active_only: bool = True) -> List[str]:
        """
        Get distinct product categories using CategoryRepository.

        This method retrieves all available categories, with an option to filter
        by active status. It leverages the CategoryRepository for proper
        separation of concerns.

        Args:
            active_only (bool): If True, only return active categories

        Returns:
            List[str]: List of category names

        Raises:
            HTTPException: If retrieval fails due to database errors
        """
        try:
            categories = await self.category_repo.list_categories(active_only=active_only)
            category_names = [category.name for category in categories]

            self.logger.log(
                message=f"Retrieved {len(category_names)} categories (active_only={active_only})",
                level="info",
                app_name="products",
            )

            return category_names

        except Exception as e:
            self.logger.log(
                message="Error retrieving product categories",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(
                status_code=500,
                detail="Failed to retrieve categories"
            )

    async def create_category(self, name: str, description: Optional[str] = None) -> Category:
        """
        Create a new product category.

        This method provides a service-layer interface for category creation
        with proper validation and logging.

        Args:
            name (str): Category name
            description (Optional[str]): Category description

        Returns:
            Category: The created category object

        Raises:
            HTTPException: If creation fails or category already exists
        """
        try:
            if not name or not name.strip():
                raise HTTPException(
                    status_code=400,
                    detail="Category name is required"
                )

            name = name.strip()

            self.logger.log(
                message=f"Creating new category: {name}",
                level="info",
                app_name="products",
            )

            category = await self.category_repo.create_category(name, description)

            self.logger.log(
                message=f"Successfully created category: {name} (ID: {category.id})",
                level="info",
                app_name="products",
            )

            return category

        except HTTPException:
            raise
        except Exception as e:
            self.logger.log(
                message=f"Unexpected error creating category: {name}",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(
                status_code=500,
                detail="Failed to create category"
            )

    async def get_category_by_name(self, name: str) -> Optional[Category]:
        """
        Retrieve a category by name.

        Args:
            name (str): Category name to search for

        Returns:
            Optional[Category]: The category if found, None otherwise
        """
        try:
            if not name or not name.strip():
                return None

            return await self.category_repo.get_category_by_name(name.strip())

        except Exception as e:
            self.logger.log(
                message=f"Error retrieving category by name: {name}",
                level="error",
                exception=e,
                app_name="products",
            )
            return None

    async def validate_product_data(self, product_data: ProductCreate) -> None:
        """
        Validate product data before creation.

        This method performs comprehensive validation of product data including:
        - Required field validation
        - Category validation
        - Business rule validation

        Args:
            product_data (ProductCreate): Product data to validate

        Raises:
            HTTPException: If validation fails
        """
        try:
            if not product_data.name or not product_data.name.strip():
                raise HTTPException(
                    status_code=400,
                    detail="Product name is required"
                )

            if product_data.price is None or product_data.price <= 0:
                raise HTTPException(
                    status_code=400,
                    detail="Product price must be greater than 0"
                )

            if product_data.categories:
                validated_categories = await self._validate_and_process_categories(
                    product_data.categories
                )

                if len(validated_categories) != len(set(validated_categories)):
                    raise HTTPException(
                        status_code=400,
                        detail="Duplicate categories are not allowed"
                    )

            self.logger.log(
                message=f"Product data validation passed for: {product_data.name}",
                level="info",
                app_name="products",
            )

        except HTTPException:
            raise
        except Exception as e:
            self.logger.log(
                message=f"Error validating product data: {product_data.name}",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(
                status_code=500,
                detail="Failed to validate product data"
            )
