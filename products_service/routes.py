from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from database import get_db
from services.product import ProductService
from schemas import (
    ProductCreate,
    ProductResponse,
    ProductUpdate,
)
from auth_dependencies import (
    require_write_permission,
    AuthenticatedUser,
)

router = APIRouter()


def get_product_service(db: AsyncSession = Depends(get_db)) -> ProductService:
    """Dependency to get product service instance."""
    return ProductService(db)



@router.get(
    "/",
    response_model=List[ProductResponse],
    summary="List products",
    description="Get a list of all products with optional filtering",
)
async def list_products(
    category: Optional[str] = Query(None, description="Filter by category"),
    in_stock: Optional[bool] = Query(None, description="Filter by stock availability"),
    search: Optional[str] = Query(
        None, description="Search products by name or description"
    ),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of items to return"
    ),
    offset: int = Query(0, ge=0, description="Number of items to skip for pagination"),
    product_service: ProductService = Depends(get_product_service),
) -> List[ProductResponse]:
    """
    List products with optional filtering and pagination.

    - **category**: Optional filter by category
    - **in_stock**: Optional filter by stock availability
    - **search**: Optional search by name or description
    - **limit**: Maximum number of items to return (1-1000)
    - **offset**: Number of items to skip for pagination
    """
    return await product_service.list_products(
        skip=offset,
        limit=limit,
        filter={
            "category": category,
            "in_stock": in_stock,
            "search": search,
        },
    )


@router.post(
    "/",
    response_model=ProductResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create product",
    description="Create a new product (requires authentication with write permission)",
)
async def create_product(
    product_data: ProductCreate,
    product_service: ProductService = Depends(get_product_service),
    current_user: AuthenticatedUser = Depends(require_write_permission),
) -> ProductResponse:
    """
    Create a new product.

    - **name**: Name of the product
    - **description**: Description of the product (optional)
    - **price**: Price of the product
    - **currency**: Currency code (default: USD)
    - **in_stock**: Whether the product is in stock (optional)
    - **stock_quantity**: Quantity in stock (optional)
    - **unity_of_measure**: Unit of measure (optional)
    - **categories**: List of category names (optional)
    - **tags**: List of tags (optional)
    - **images**: List of image URLs (optional)
    """
    return await product_service.create_product(current_user, product_data)


@router.get(
    "/search",
    response_model=List[ProductResponse],
    summary="Search products",
    description="Search products by name, description, or tags",
)
async def search_products(
    q: str = Query(..., min_length=1, description="Search query"),
    limit: int = Query(50, ge=1, le=1000, description="Maximum number of results"),
    product_service: ProductService = Depends(get_product_service),
) -> List[ProductResponse]:
    """
    Search products by name, description, or tags.

    - **q**: Search query (minimum 1 character)
    - **limit**: Maximum number of results to return
    """
    return await product_service.search_products(q, limit)


@router.get(
    "/categories",
    summary="Get all categories",
    description="Get all product categories",
)
async def get_product_categories(
    product_service: ProductService = Depends(get_product_service),
) -> List[str]:
    """
    Get all available product categories.
    """
    return await product_service.get_categories()


@router.get(
    "/{product_id}",
    response_model=ProductResponse,
    summary="Get product",
    description="Retrieve a single product by ID",
)
async def get_product(
    product_id: int,
    product_service: ProductService = Depends(get_product_service),
) -> ProductResponse:
    """
    Get a single product by ID.

    - **product_id**: The ID of the product to retrieve
    """
    return await product_service.get_product(product_id)

@router.put(
    "/{product_id}",
    response_model=ProductResponse,
    summary="Update product",
    description="Update an existing product (requires authentication with write permission)",
)
async def update_product(
    product_id: int,
    update_data: ProductUpdate,
    product_service: ProductService = Depends(get_product_service),
    current_user: AuthenticatedUser = Depends(require_write_permission),
) -> ProductResponse:
    """
    Update an existing product.

    - **product_id**: The ID of the product to update
    - **name**: Updated name of the product (optional)
    - **description**: Updated description of the product (optional)
    - **price**: Updated price of the product (optional)
    - **currency**: Updated currency code (optional)
    - **in_stock**: Updated stock availability (optional)
    - **stock_quantity**: Updated quantity in stock (optional)
    - **unity_of_measure**: Updated unit of measure (optional)
    - **categories**: Updated list of category names (optional)
    - **tags**: Updated list of tags (optional)
    - **images**: Updated list of image URLs (optional)
    """
    return await product_service.update_product(product_id, update_data, current_user)


@router.delete(
    "/{product_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete product",
    description="Delete a product (requires authentication with write permission)",
)
async def delete_product(
    product_id: int,
    product_service: ProductService = Depends(get_product_service),
    current_user: AuthenticatedUser = Depends(require_write_permission),
) -> None:
    """
    Delete a product.

    - **product_id**: The ID of the product to delete
    """
    await product_service.delete_product(product_id, current_user)


# Additional utility endpoints


@router.get(
    "/{product_id}/availability",
    summary="Check product availability",
    description="Check if a product is available",
)
async def check_product_availability(
    product_id: int,
    product_service: ProductService = Depends(get_product_service),
) -> dict:
    """
    Check if a product is available and get stock information.

    - **product_id**: The ID of the product to check
    """
    product = await product_service.get_product(product_id)
    return {
        "product_id": product.id,
        "name": product.name,
        "in_stock": product.in_stock,
        "stock_quantity": product.stock_quantity,
        "is_available": product.in_stock and product.stock_quantity > 0,
    }

