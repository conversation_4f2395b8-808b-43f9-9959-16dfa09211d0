"""
JWT Authentication Middleware for Product Service.

This module provides JWT token validation using an external authentication service.
It includes token extraction, verification, and caching mechanisms for optimal performance.
"""

import asyncio
import time
from typing import Optional, Dict, Any
import httpx
from fastapi import HTTPException, status
from shared.config import settings
from shared.core.utils.logging import LoggingService
from products_service.auth_models import (
    TokenVerificationResponse,
    JWKSResponse,
    AuthenticatedUser,
    AuthenticationError,
    AuthorizationError
)


class AuthenticationService:
    """
    Service for handling JWT authentication with external auth service.
    
    This service provides:
    - Token extraction from Authorization headers
    - Token verification via external auth service
    - JWKS public key retrieval and caching
    - User information extraction from verified tokens
    """
    
    def __init__(self):
        self.logger = LoggingService()
        self._jwks_cache: Dict[str, Any] = {}
        self._token_cache: Dict[str, Any] = {}
        self._client: Optional[httpx.AsyncClient] = None
    
    async def _get_http_client(self) -> httpx.AsyncClient:
        """Get or create HTTP client for external requests."""
        if self._client is None:
            self._client = httpx.AsyncClient(
                timeout=httpx.Timeout(10.0),
                limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
            )
        return self._client
    
    async def close(self):
        """Close HTTP client connections."""
        if self._client:
            await self._client.aclose()
            self._client = None
    
    def extract_token_from_header(self, authorization: Optional[str]) -> Optional[str]:
        """
        Extract JWT token from Authorization header.
        
        Args:
            authorization: Authorization header value
            
        Returns:
            JWT token string or None if not found/invalid format
        """
        if not authorization:
            return None
        
        try:
            scheme, token = authorization.split(" ", 1)
            if scheme.lower() != "bearer":
                return None
            return token.strip()
        except ValueError:
            return None
    
    async def get_jwks_public_key(self) -> str:
        """
        Retrieve public key from JWKS endpoint with caching.
        
        Returns:
            Public key in PEM format
            
        Raises:
            AuthenticationError: If unable to retrieve public key
        """
        cache_key = "jwks_public_key"
        current_time = time.time()
        
        # Check cache first
        if cache_key in self._jwks_cache:
            cached_data = self._jwks_cache[cache_key]
            if current_time - cached_data["timestamp"] < settings.AUTH_CACHE_TTL_SECONDS:
                return cached_data["public_key"]
        
        try:
            client = await self._get_http_client()
            response = await client.get(settings.AUTH_JWKS_URL)
            response.raise_for_status()
            
            jwks_data = JWKSResponse(**response.json())
            
            # Cache the public key
            self._jwks_cache[cache_key] = {
                "public_key": jwks_data.public_key,
                "timestamp": current_time
            }
            
            self.logger.log(
                message="Successfully retrieved JWKS public key",
                level="info",
                app_name="products_auth"
            )
            
            return jwks_data.public_key
            
        except httpx.HTTPError as e:
            self.logger.log(
                message=f"Failed to retrieve JWKS public key: {str(e)}",
                level="error",
                exception=e,
                app_name="products_auth"
            )
            raise AuthenticationError("Unable to retrieve authentication keys")
        except Exception as e:
            self.logger.log(
                message=f"Unexpected error retrieving JWKS: {str(e)}",
                level="error",
                exception=e,
                app_name="products_auth"
            )
            raise AuthenticationError("Authentication service error")
    
    async def verify_token_with_service(self, token: str) -> TokenVerificationResponse:
        """
        Verify token with external authentication service.
        
        Args:
            token: JWT token to verify
            
        Returns:
            Token verification response
            
        Raises:
            AuthenticationError: If token verification fails
        """
        cache_key = f"token_{hash(token)}"
        current_time = time.time()
        
        # Check cache first
        if cache_key in self._token_cache:
            cached_data = self._token_cache[cache_key]
            if current_time - cached_data["timestamp"] < settings.AUTH_TOKEN_CACHE_TTL_SECONDS:
                return cached_data["response"]
        
        try:
            client = await self._get_http_client()
            response = await client.post(
                settings.AUTH_TOKEN_VERIFY_URL,
                json={"token": token},
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 401:
                raise AuthenticationError("Invalid or expired token")
            
            response.raise_for_status()
            verification_response = TokenVerificationResponse(**response.json())
            
            if not verification_response.active:
                raise AuthenticationError("Token is not active")
            
            # Cache the verification response
            self._token_cache[cache_key] = {
                "response": verification_response,
                "timestamp": current_time
            }
            
            self.logger.log(
                message=f"Successfully verified token for user: {verification_response.sub}",
                level="info",
                app_name="products_auth"
            )
            
            return verification_response
            
        except httpx.HTTPError as e:
            self.logger.log(
                message=f"Token verification failed: {str(e)}",
                level="error",
                exception=e,
                app_name="products_auth"
            )
            raise AuthenticationError("Token verification failed")
        except AuthenticationError:
            raise
        except Exception as e:
            self.logger.log(
                message=f"Unexpected error during token verification: {str(e)}",
                level="error",
                exception=e,
                app_name="products_auth"
            )
            raise AuthenticationError("Authentication service error")
    
    async def authenticate_user(self, token: str) -> AuthenticatedUser:
        """
        Authenticate user using JWT token.
        
        Args:
            token: JWT token to authenticate
            
        Returns:
            Authenticated user information
            
        Raises:
            AuthenticationError: If authentication fails
        """
        try:
            # Verify token with external service
            verification_response = await self.verify_token_with_service(token)
            
            # Create authenticated user object
            authenticated_user = AuthenticatedUser(
                email=verification_response.sub,
                roles=verification_response.roles,
                scope=verification_response.scope,
                is_authenticated=True
            )
            
            return authenticated_user
            
        except AuthenticationError:
            raise
        except Exception as e:
            self.logger.log(
                message=f"User authentication failed: {str(e)}",
                level="error",
                exception=e,
                app_name="products_auth"
            )
            raise AuthenticationError("Authentication failed")


# Global authentication service instance
auth_service = AuthenticationService()


async def cleanup_auth_service():
    """Cleanup function for authentication service."""
    await auth_service.close()
