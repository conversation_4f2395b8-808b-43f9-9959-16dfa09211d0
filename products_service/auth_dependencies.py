"""
Authentication dependencies for FastAPI routes.

This module provides dependency functions for protecting routes with JWT authentication
and role-based access control.
"""

from typing import List, Optional, Callable
from fastapi import Depends, HTTPException, status, Header
from products_service.auth_middleware import auth_service
from products_service.auth_models import AuthenticatedUser, AuthenticationError, AuthorizationError


async def get_current_user(
    authorization: Optional[str] = Header(None, description="Bearer token for authentication")
) -> AuthenticatedUser:
    """
    Dependency to get the current authenticated user from JWT token.
    
    This dependency extracts and validates the JWT token from the Authorization header,
    verifies it with the external authentication service, and returns the authenticated user.
    
    Args:
        authorization: Authorization header containing Bearer token
        
    Returns:
        AuthenticatedUser: Authenticated user information
        
    Raises:
        HTTPException: 401 if authentication fails
    """
    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authorization header is required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Extract token from Authorization header
    token = auth_service.extract_token_from_header(authorization)
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authorization header format. Expected 'Bearer <token>'",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        # Authenticate user with token
        user = await auth_service.authenticate_user(token)
        return user
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=e.message,
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication service error",
        )


def require_roles(required_roles: List[str]) -> Callable:
    """
    Dependency factory to require specific roles for route access.
    
    This creates a dependency that checks if the authenticated user has any of the
    required roles. If not, it raises a 403 Forbidden error.
    
    Args:
        required_roles: List of roles that are allowed to access the route
        
    Returns:
        Dependency function that validates user roles
        
    Example:
        @router.post("/admin-only")
        async def admin_endpoint(
            user: AuthenticatedUser = Depends(require_roles(["admin", "super_admin"]))
        ):
            pass
    """
    async def check_roles(user: AuthenticatedUser = Depends(get_current_user)) -> AuthenticatedUser:
        if not user.has_any_role(required_roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required roles: {', '.join(required_roles)}",
            )
        return user
    
    return check_roles


def require_scopes(required_scopes: List[str]) -> Callable:
    """
    Dependency factory to require specific scopes/permissions for route access.
    
    This creates a dependency that checks if the authenticated user has any of the
    required scopes. If not, it raises a 403 Forbidden error.
    
    Args:
        required_scopes: List of scopes that are allowed to access the route
        
    Returns:
        Dependency function that validates user scopes
        
    Example:
        @router.post("/write-only")
        async def write_endpoint(
            user: AuthenticatedUser = Depends(require_scopes(["write", "admin"]))
        ):
            pass
    """
    async def check_scopes(user: AuthenticatedUser = Depends(get_current_user)) -> AuthenticatedUser:
        if not user.has_any_scope(required_scopes):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required scopes: {', '.join(required_scopes)}",
            )
        return user
    
    return check_scopes


def require_write_access() -> Callable:
    """
    Convenience dependency for routes that require write access.
    
    This checks for 'write' scope or 'admin' role, which are typically
    required for create, update, and delete operations.
    
    Returns:
        Dependency function that validates write access
    """
    async def check_write_access(user: AuthenticatedUser = Depends(get_current_user)) -> AuthenticatedUser:
        if not (user.has_scope("write") or user.has_role("admin")):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Write access required. Need 'write' scope or 'admin' role.",
            )
        return user
    
    return check_write_access


def require_admin_access() -> Callable:
    """
    Convenience dependency for routes that require admin access.
    
    This checks for 'admin' role, which is typically required for
    administrative operations.
    
    Returns:
        Dependency function that validates admin access
    """
    async def check_admin_access(user: AuthenticatedUser = Depends(get_current_user)) -> AuthenticatedUser:
        if not user.has_role("admin"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required.",
            )
        return user
    
    return check_admin_access


# Convenience dependencies for common use cases
require_authenticated_user = get_current_user
require_write_permission = require_write_access()
require_admin_permission = require_admin_access()
