"""
Configuration settings for the products service.

This module contains all configuration settings specific to the products service,
making it independent from shared configuration.
"""

import os
from typing import Optional


class ProductServiceSettings:
    """Configuration settings for the products service."""
    
    def __init__(self):
        # Database Configuration
        self.DATABASE_URL: str = os.getenv(
            "DATABASE_URL", 
            "sqlite:///./products.db"
        )
        
        # External Authentication Service Configuration
        self.AUTH_SERVICE_BASE_URL: str = os.getenv(
            "AUTH_SERVICE_BASE_URL",
            "https://fehdan-auth-service.onrender.com/api/v1/auth"
        )
        self.AUTH_TOKEN_VERIFY_URL: str = os.getenv(
            "AUTH_TOKEN_VERIFY_URL",
            "https://fehdan-auth-service.onrender.com/api/v1/auth/verify-token"
        )
        self.AUTH_JWKS_URL: str = os.getenv(
            "AUTH_JWKS_URL",
            "https://fehdan-auth-service.onrender.com/api/v1/auth/.well-known/jwks.json"
        )
        
        # Authentication Cache Configuration
        self.AUTH_CACHE_TTL_SECONDS: int = int(os.getenv("AUTH_CACHE_TTL_SECONDS", "300"))  # 5 minutes
        self.AUTH_TOKEN_CACHE_TTL_SECONDS: int = int(os.getenv("AUTH_TOKEN_CACHE_TTL_SECONDS", "60"))  # 1 minute
        
        # Application Configuration
        self.DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"
        self.PROJECT_NAME: str = os.getenv("PROJECT_NAME", "Products Service")
        
        # CORS Configuration
        self.ALLOWED_ORIGINS: list = os.getenv(
            "ALLOWED_ORIGINS", 
            "http://localhost:3000,http://localhost:3001"
        ).split(",")
    
    @property
    def async_database_url(self) -> str:
        """Convert DATABASE_URL to use async driver."""
        url = self.DATABASE_URL
        
        # Convert postgresql:// to postgresql+asyncpg:// for async compatibility
        if url.startswith("postgresql://"):
            url = url.replace("postgresql://", "postgresql+asyncpg://", 1)
        elif url.startswith("postgres://"):
            url = url.replace("postgres://", "postgresql+asyncpg://", 1)
        # Convert sqlite:// to sqlite+aiosqlite:// for async compatibility
        elif url.startswith("sqlite://"):
            url = url.replace("sqlite://", "sqlite+aiosqlite://", 1)
        
        return url


# Global settings instance
settings = ProductServiceSettings()
