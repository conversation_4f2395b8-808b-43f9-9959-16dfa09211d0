from sqlalchemy import (
    Column,
    Integer,
    String,
    Boolean,
    DateTime,
    ForeignKey,
    Text,
    DECIMAL,
    JSON,
    Enum,
    Table,
)
from sqlalchemy.orm import relationship
from products_service.database import Base
import datetime
import enum


# Association table for many-to-many relationship between products and categories
product_categories = Table(
    'product_categories',
    Base.metadata,
    Column('product_id', Integer, ForeignKey('products.id'), primary_key=True),
    Column('category_id', Integer, ForeignKey('categories.id'), primary_key=True)
)


class Category(Base):
    __tablename__ = "categories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True, index=True)
    description = Column(Text)
    is_active = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(
        DateTime,
        default=datetime.datetime.utcnow,
        onupdate=datetime.datetime.utcnow,
    )

    # Relationships
    products = relationship("Product", secondary=product_categories, back_populates="categories")


class AnimalType(str, enum.Enum):
    CHICKEN = "chicken"
    BEEF = "beef"
    GOAT = "goat"
    PORK = "pork"


class ProcessingType(str, enum.Enum):
    FRESH = "fresh"
    FROZEN = "frozen"
    CURED = "cured"
    SMOKED = "smoked"
    PROCESSED = "processed"


class PreparationType(str, enum.Enum):
    BONE_IN = "bone_in"
    BONELESS = "boneless"
    BONELESS_SKINLESS = "boneless_skinless"
    SKIN_ON = "skin_on"
    GROUND = "ground"
    WHOLE = "whole"


class PackagingType(str, enum.Enum):
    TRAY = "tray"
    VACUUM = "vacuum"
    BULK = "bulk"
    INDIVIDUAL = "individual"


class Product(Base):
    __tablename__ = "products"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text)
    price = Column(DECIMAL(10, 2), nullable=False)
    currency = Column(String(3), default="USD")
    in_stock = Column(Boolean, default=True)
    stock_quantity = Column(Integer, default=0)
    unity_of_measure = Column(String(20), default="lb")  # lb, kg, piece, pack
    tags = Column(JSON)  # Store as JSON array
    images = Column(JSON)  # Store as JSON array of image URLs

    # Timestamps
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(
        DateTime,
        default=datetime.datetime.utcnow,
        onupdate=datetime.datetime.utcnow,
    )

    # Relationships
    categories = relationship("Category", secondary=product_categories, back_populates="products")
    meat_details = relationship(
        "MeatDetails",
        back_populates="product",
        uselist=False,
        cascade="all, delete-orphan",
    )
    nutritional_info = relationship(
        "NutritionalInfo",
        back_populates="product",
        uselist=False,
        cascade="all, delete-orphan",
    )
    packaging = relationship(
        "Packaging",
        back_populates="product",
        uselist=False,
        cascade="all, delete-orphan",
    )


class MeatDetails(Base):
    __tablename__ = "meat_details"

    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(Integer, ForeignKey("products.id"), unique=True)

    animal_type = Column(Enum(AnimalType), nullable=False)
    cut_type = Column(String(100), nullable=False)  # breast, thigh, ribeye, etc.
    grade = Column(String(20))  # A, AA, AAA, Prime, Choice, Select
    processing_type = Column(Enum(ProcessingType), default=ProcessingType.FRESH)
    preparation = Column(Enum(PreparationType))
    packaging_type = Column(Enum(PackagingType), default=PackagingType.TRAY)
    origin = Column(String(255))
    feed_type = Column(String(50))  # grass_fed, grain_fed, organic, free_range
    certifications = Column(JSON)  # Store as JSON array
    lean_ratio = Column(String(10))  # For ground meat: 80/20, 90/10, etc.

    # Storage and cooking information
    shelf_life_fresh = Column(String(50))
    shelf_life_frozen = Column(String(50))
    storage_instructions = Column(Text)
    cooking_instructions = Column(Text)

    # Relationship
    product = relationship("Product", back_populates="meat_details")


class NutritionalInfo(Base):
    __tablename__ = "nutritional_info"

    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(Integer, ForeignKey("products.id"), unique=True)

    serving_size = Column(String(50))  # "4 oz (113g)"
    calories = Column(Integer)
    protein = Column(String(20))  # "35g"
    fat = Column(String(20))  # "4g"
    saturated_fat = Column(String(20))  # "1g"
    cholesterol = Column(String(20))  # "85mg"
    sodium = Column(String(20))  # "75mg"
    carbohydrates = Column(String(20))  # "0g"
    fiber = Column(String(20))  # "0g"
    sugar = Column(String(20))  # "0g"

    # Relationship
    product = relationship("Product", back_populates="nutritional_info")


class Packaging(Base):
    __tablename__ = "packaging"

    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(Integer, ForeignKey("products.id"), unique=True)

    weight = Column(String(50))  # "1.5 lb"
    package_size = Column(String(50))  # individual, family_pack, bulk
    packaged_date = Column(DateTime)
    expiration_date = Column(DateTime)
    batch_number = Column(String(100))

    # Relationship
    product = relationship("Product", back_populates="packaging")
