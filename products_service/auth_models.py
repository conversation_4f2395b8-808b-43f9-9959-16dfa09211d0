"""
Authentication models for JWT token validation and user information.

This module defines Pydantic models for handling authentication responses
from the external authentication service.
"""

from typing import List, Optional
from pydantic import BaseModel, Field


class TokenVerificationResponse(BaseModel):
    """
    Response model for token verification from external auth service.
    
    This model represents the response format from the auth service's
    verify-token endpoint.
    """
    active: bool = Field(..., description="Whether the token is active and valid")
    sub: str = Field(..., description="Subject (user email/identifier)")
    roles: List[str] = Field(default_factory=list, description="User roles")
    scope: List[str] = Field(default_factory=list, description="User permissions/scopes")
    
    class Config:
        json_schema_extra = {
            "example": {
                "active": True,
                "sub": "<EMAIL>",
                "roles": ["customer"],
                "scope": ["read", "write"]
            }
        }


class JWKSResponse(BaseModel):
    """
    Response model for JWKS (JSON Web Key Set) from external auth service.
    
    This model represents the response format from the auth service's
    JWKS endpoint containing the public key for token verification.
    """
    public_key: str = Field(..., description="RSA public key in PEM format")
    
    class Config:
        json_schema_extra = {
            "example": {
                "public_key": "-----BEGIN PUBLIC KEY-----\n[RSA public key content]\n-----END PUBLIC KEY-----"
            }
        }


class AuthenticatedUser(BaseModel):
    """
    Model representing an authenticated user with roles and permissions.
    
    This model is used internally after successful token verification
    to represent the current user's authentication state.
    """
    email: str = Field(..., description="User email address")
    roles: List[str] = Field(default_factory=list, description="User roles")
    scope: List[str] = Field(default_factory=list, description="User permissions/scopes")
    is_authenticated: bool = Field(default=True, description="Authentication status")
    
    def has_role(self, role: str) -> bool:
        """Check if user has a specific role."""
        return role in self.roles
    
    def has_scope(self, scope: str) -> bool:
        """Check if user has a specific scope/permission."""
        return scope in self.scope
    
    def has_any_role(self, roles: List[str]) -> bool:
        """Check if user has any of the specified roles."""
        return any(role in self.roles for role in roles)
    
    def has_any_scope(self, scopes: List[str]) -> bool:
        """Check if user has any of the specified scopes."""
        return any(scope in self.scope for scope in scopes)


class AuthenticationError(Exception):
    """Custom exception for authentication errors."""
    
    def __init__(self, message: str, status_code: int = 401):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


class AuthorizationError(Exception):
    """Custom exception for authorization errors."""
    
    def __init__(self, message: str, status_code: int = 403):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)
