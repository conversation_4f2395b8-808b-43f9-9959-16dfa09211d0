from fastapi import HTTPException
from products_service.model import Product
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import delete
from sqlalchemy.orm import selectinload
from products_service.logging_utils import LoggingService
from products_service.schemas import ProductCreate, ProductUpdate, ProductResponse


class ProductRepository:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.logger = LoggingService()

    async def create_product(self, product_data: ProductCreate) -> ProductResponse:
        """Create a new product."""
        try:
            product_dict = product_data.model_dump()
            product_dict.pop('categories', None)

            db_product = Product(**product_dict)
            self.db.add(db_product)
            await self.db.commit()
            await self.db.refresh(db_product, ['categories'])

            return ProductResponse.model_validate(db_product)
        except Exception as e:
            await self.db.rollback()
            self.logger.log(
                message="Error creating product",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(status_code=500, detail="Error creating product")

    async def get_product_by_id(self, product_id: int) -> ProductResponse:
        """Retrieve a product by ID."""
        try:
            result = await self.db.execute(
                select(Product)
                .options(selectinload(Product.categories))
                .filter_by(id=product_id)
            )
            product = result.scalar_one_or_none()
            if not product:
                raise HTTPException(status_code=404, detail="Product not found")
            return ProductResponse.model_validate(product)
        except Exception as e:
            self.logger.log(
                message="Error retrieving product",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(status_code=500, detail="Error retrieving product")

    async def update_product(
        self, product_id: int, product_data: ProductUpdate
    ) -> ProductResponse:
        """Update product details."""
        try:
            result = await self.db.execute(
                select(Product)
                .options(selectinload(Product.categories))
                .where(Product.id == product_id)
            )
            product = result.scalar_one_or_none()

            if not product:
                raise HTTPException(status_code=404, detail="Product not found")

            update_data = product_data.model_dump(exclude_unset=True, exclude_none=True)
            update_data.pop('categories', None)

            for key, value in update_data.items():
                setattr(product, key, value)

            await self.db.commit()
            await self.db.refresh(product, ['categories'])

            return ProductResponse.model_validate(product)
        except Exception as e:
            await self.db.rollback()
            self.logger.log(
                message="Error updating product",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(status_code=500, detail="Error updating product")

    async def delete_product(self, product_id: int) -> None:
        """Delete a product by ID."""
        try:
            result = await self.db.execute(select(Product).filter_by(id=product_id))
            product = result.scalar_one_or_none()
            if not product:
                raise HTTPException(status_code=404, detail="Product not found")

            await self.db.execute(delete(Product).where(Product.id == product_id))
            await self.db.commit()
        except Exception as e:
            await self.db.rollback()
            self.logger.log(
                message="Error deleting product",
                level="error",
                exception=e,
                app_name="products",
            )
            raise HTTPException(status_code=500, detail="Error deleting product")
